// Firebase configuration for Web
// This uses the Firebase Web SDK instead of React Native Firebase

import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';

// Firebase config - same project as mobile app
const firebaseConfig = {
  apiKey: "AIzaSyBV99Kd9kmI7p_E11HG_lC4vIKPvjqKZ_I",
  authDomain: "yellowtaxi-rides.firebaseapp.com",
  projectId: "yellowtaxi-rides",
  storageBucket: "yellowtaxi-rides.firebasestorage.app",
  messagingSenderId: "926847755164",
  appId: "1:926847755164:web:bc7677ccd2366aed529e59"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const firebaseAuth = getAuth(app);
export const firebaseFirestore = getFirestore(app);

// For development, you can connect to emulators
if (process.env.NODE_ENV === 'development') {
  // Uncomment these lines if you want to use Firebase emulators
  // connectAuthEmulator(firebaseAuth, 'http://localhost:9099');
  // connectFirestoreEmulator(firebaseFirestore, 'localhost', 8080);
}

// Export FieldValue equivalent for web
export const FieldValue = {
  serverTimestamp: () => ({ serverTimestamp: true }),
  delete: () => ({ delete: true }),
  increment: (n: number) => ({ increment: n }),
  arrayUnion: (...elements: any[]) => ({ arrayUnion: elements }),
  arrayRemove: (...elements: any[]) => ({ arrayRemove: elements }),
};

// Mock types for compatibility
export type User = any;
export type UserCredential = any;
export type ConfirmationResult = any;
export type DocumentSnapshot = any;
export type QuerySnapshot = any;

// Helper function to check if Firebase is configured
export const isFirebaseConfigured = (): boolean => {
  try {
    return !!(firebaseAuth && firebaseFirestore);
  } catch (error) {
    console.warn('Firebase configuration check failed:', error);
    return false;
  }
};

// Debug Firebase configuration (development only)
if (process.env.NODE_ENV === 'development') {
  try {
    console.log('🔥 Firebase Web Configuration Status:', {
      isConfigured: isFirebaseConfigured(),
      hasAuth: !!firebaseAuth,
      hasFirestore: !!firebaseFirestore,
      authCurrentUser: firebaseAuth.currentUser?.uid || 'No user',
    });
  } catch (error) {
    console.warn('Firebase debug info failed:', error);
  }
}
