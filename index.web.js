import React from 'react';
import { AppRegistry } from 'react-native';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './src/store';
import { WelcomeScreen } from './src/screens/WelcomeScreen';
import { SplashScreen } from './src/screens/SplashScreen';

console.log('🚀 YellowTaxi React Native Web app starting...');

// Simplified web app component that shows the main screens
const WebApp = () => {
  return (
    <Provider store={store}>
      <PersistGate loading={<SplashScreen />} persistor={persistor}>
        <WelcomeScreen />
      </PersistGate>
    </Provider>
  );
};

// Register the simplified app for web
AppRegistry.registerComponent('yellowtaxiapp', () => WebApp);

// Get the root element
const container = document.getElementById('root');

if (container) {
  // Run the React Native app on web
  AppRegistry.runApplication('yellowtaxiapp', {
    initialProps: {},
    rootTag: container,
  });
  console.log('✅ React Native app registered and running on web');
} else {
  console.error('❌ Root element not found');
}


