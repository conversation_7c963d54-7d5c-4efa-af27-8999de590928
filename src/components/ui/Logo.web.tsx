import React from 'react';
import { View, StyleSheet } from 'react-native';

interface LogoProps {
  size?: number;
  color?: string;
}

export const Logo: React.FC<LogoProps> = ({ 
  size = 80, 
  color = '#FFC100' 
}) => {
  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 381 381"
        style={{ width: '100%', height: '100%' }}
      >
        <defs>
          <clipPath id="clipPath2">
            <path
              d="M 0,1080 H 1080 V 0 H 0 Z"
              transform="translate(-496.91501,-715.32232)"
            />
          </clipPath>
          <clipPath id="clipPath4">
            <path
              d="M 0,1080 H 1080 V 0 H 0 Z"
              transform="translate(-555.15721,-333.13091)"
            />
          </clipPath>
          <clipPath id="clipPath6">
            <path
              d="M 0,1080 H 1080 V 0 H 0 Z"
              transform="translate(-603.98441,-460.59771)"
            />
          </clipPath>
        </defs>
        
        <g transform="matrix(0.26458333,0,0,0.26458334,1e-5,0)">
          <g>
            {/* Main taxi body - black */}
            <path
              d="M 0,0 C 14.663,12.729 29.277,25.458 46.823,41.157 L 49.737,43.77 17.143,42.436 15.654,41.1 C 6.157,32.711 -2.343,25.312 -10.564,18.157 l -4.875,-4.244 C -21.61,8.561 -27.525,3.404 -33.251,-1.598 c -2.173,1.987 -4.749,4.357 -7.78,7.143 -9.308,8.546 -22.472,20.617 -38.776,35.467 l -1.819,1.513 -0.371,0.012 c -9.796,0.273 -31.759,1.233 -31.759,1.233 l 3.207,-2.771 c 19.905,-18.061 36.17,-32.967 48.046,-43.85 l 5.611,-5.143 c 8.795,-8.064 13.867,-12.71 15.823,-14.406 l 7.792,-6.808 7.73,6.876 c 7.619,6.758 15.675,13.756 24.204,21.165 z"
              fill="#000000"
              transform="matrix(1.3333333,0,0,-1.3333333,662.55333,486.23693)"
              clipPath="url(#clipPath2)"
            />
            
            {/* Yellow taxi top part */}
            <path
              d="M 0,0 C 14.663,12.729 29.277,25.458 46.823,41.157 L 49.737,43.77 17.143,42.436 15.654,41.1 C 6.157,32.711 -2.343,25.312 -10.564,18.157 l -4.875,-4.244 C -21.61,8.561 -27.525,3.404 -33.251,-1.598 c -2.173,1.987 -4.749,4.357 -7.78,7.143 -9.308,8.546 -22.472,20.617 -38.776,35.467 l -1.819,1.513 -0.371,0.012 c -9.796,0.273 -31.759,1.233 -31.759,1.233 l 3.207,-2.771 c 19.905,-18.061 36.17,-32.967 48.046,-43.85 l 5.611,-5.143 c 8.795,-8.064 13.867,-12.71 15.823,-14.406 l 7.792,-6.808 7.73,6.876 c 7.619,6.758 15.675,13.756 24.204,21.165 z"
              fill={color}
              transform="matrix(1.3333333,0,0,-1.3333333,740.2096,995.82547)"
              clipPath="url(#clipPath4)"
            />
            
            {/* Yellow taxi main body */}
            <path
              d="M 0,0 C 14.663,12.729 29.277,25.458 46.823,41.157 L 49.737,43.77 17.143,42.436 15.654,41.1 C 6.157,32.711 -2.343,25.312 -10.564,18.157 l -4.875,-4.244 C -21.61,8.561 -27.525,3.404 -33.251,-1.598 c -2.173,1.987 -4.749,4.357 -7.78,7.143 -9.308,8.546 -22.472,20.617 -38.776,35.467 l -1.819,1.513 -0.371,0.012 c -9.796,0.273 -31.759,1.233 -31.759,1.233 l 3.207,-2.771 c 19.905,-18.061 36.17,-32.967 48.046,-43.85 l 5.611,-5.143 c 8.795,-8.064 13.867,-12.71 15.823,-14.406 l 7.792,-6.808 7.73,6.876 c 7.619,6.758 15.675,13.756 24.204,21.165 z"
              fill={color}
              transform="matrix(1.3333333,0,0,-1.3333333,805.31253,825.86973)"
              clipPath="url(#clipPath6)"
            />
          </g>
        </g>
      </svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
