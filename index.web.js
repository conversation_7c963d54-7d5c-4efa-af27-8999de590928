// Simple vanilla JavaScript approach to test if the issue is with React
console.log('🚀 YellowTaxi Web app starting...');
console.log('Current URL:', window.location.href);
console.log('Document ready state:', document.readyState);

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded');

  const container = document.getElementById('root');
  console.log('Container found:', !!container);

  if (container) {
    container.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
      ">
        <div style="
          background-color: #FFC100;
          padding: 20px;
          border-radius: 10px;
          text-align: center;
          box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        ">
          <h1 style="margin: 0 0 10px 0; color: #000;">🚕 YellowTaxi</h1>
          <p style="margin: 0; color: #333;">React Native App Running on Web!</p>
          <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
            This is a web version of the YellowTaxi mobile app
          </p>
        </div>
      </div>
    `;
    console.log('Content rendered');
  } else {
    console.error('Root element not found');
  }
});

// Also try immediate execution in case DOMContentLoaded already fired
if (document.readyState === 'loading') {
  console.log('Document still loading, waiting for DOMContentLoaded');
} else {
  console.log('Document already loaded, executing immediately');
  renderApp();
}

// Function to render the app
function renderApp() {
  console.log('Attempting to render app...');
  const container = document.getElementById('root');
  console.log('Container element:', container);

  if (container) {
    container.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
      ">
        <div style="
          background-color: #FFC100;
          padding: 20px;
          border-radius: 10px;
          text-align: center;
          box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        ">
          <h1 style="margin: 0 0 10px 0; color: #000;">🚕 YellowTaxi</h1>
          <p style="margin: 0; color: #333;">React Native App Running on Web!</p>
          <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
            This is a web version of the YellowTaxi mobile app
          </p>
          <p style="margin: 10px 0 0 0; font-size: 12px; color: #999;">
            Bundle loaded successfully at ${new Date().toLocaleTimeString()}
          </p>
        </div>
      </div>
    `;
    console.log('✅ App rendered successfully!');
  } else {
    console.error('❌ Root element not found');
    // Fallback: try to create the root element
    document.body.innerHTML = `
      <div id="root" style="height: 100vh; display: flex; align-items: center; justify-content: center; background: #f5f5f5;">
        <div style="background: #FFC100; padding: 20px; border-radius: 10px; text-align: center;">
          <h1>🚕 YellowTaxi</h1>
          <p>Web App Loaded (Fallback Mode)</p>
        </div>
      </div>
    `;
  }
}

// Try multiple times to ensure it works
setTimeout(renderApp, 100);
setTimeout(renderApp, 500);
