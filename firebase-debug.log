[debug] [2025-09-21T23:05:23.636Z] ----------------------------------------------------------------------
[debug] [2025-09-21T23:05:23.640Z] Command:       /usr/local/bin/node /Users/<USER>/.nvm/versions/node/v22.6.0/bin/firebase login --no-localhost
[debug] [2025-09-21T23:05:23.640Z] CLI Version:   14.2.1
[debug] [2025-09-21T23:05:23.640Z] Platform:      darwin
[debug] [2025-09-21T23:05:23.640Z] Node Version:  v23.0.0
[debug] [2025-09-21T23:05:23.641Z] Time:          Mon Sep 22 2025 02:05:23 GMT+0300 (GMT+03:00)
[debug] [2025-09-21T23:05:23.641Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-09-21T23:05:23.645Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[info] i  Firebase optionally collects CLI and Emulator Suite usage and error reporting information to help improve our products. Data is collected in accordance with Google's privacy policy (https://policies.google.com/privacy) and is not used to identify you.
 
[debug] [2025-09-21T23:05:24.479Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-09-21T23:05:24.480Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
[info] i  To change your data collection preference at any time, run `firebase logout` and log in again. 
[debug] [2025-09-21T23:05:57.210Z] >>> [apiv2][query] POST https://auth.firebase.tools/attest [none]
[debug] [2025-09-21T23:05:57.210Z] >>> [apiv2][body] POST https://auth.firebase.tools/attest {"session_id":"7c5687bc-00e1-4083-b0b4-93ba6389ff9f"}
[debug] [2025-09-21T23:05:57.948Z] <<< [apiv2][status] POST https://auth.firebase.tools/attest 200
[debug] [2025-09-21T23:05:57.948Z] <<< [apiv2][body] POST https://auth.firebase.tools/attest {"token":"nITqv_c3e2kWcfDWMllKx3EEYdI3pjiuqQVXmCUZEgM"}
[info] 
[info] To sign in to the Firebase CLI:
[info] 
[info] 1. Take note of your session ID:
[info] 
[info]    7C568
[info] 
[info] 2. Visit the URL below on any device and follow the instructions to get your code:
[info] 
[info]    https://auth.firebase.tools/login?code_challenge=wOctBsTkNcAqRXFu8ff8rU-pbd9no0b9uCRsYi_y-zM&session=7c5687bc-00e1-4083-b0b4-93ba6389ff9f&attest=nITqv_c3e2kWcfDWMllKx3EEYdI3pjiuqQVXmCUZEgM
[info] 
[info] 3. Paste or enter the authorization code below once you have it:
[info] 
