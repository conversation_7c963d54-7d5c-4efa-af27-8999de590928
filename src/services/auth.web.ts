// Web-compatible authentication service
// This provides the same interface as the mobile auth service but uses Firebase Web SDK

import { 
  signInWithPhoneNumber, 
  RecaptchaVerifier, 
  ConfirmationResult,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User
} from 'firebase/auth';
import { doc, setDoc, getDoc, serverTimestamp } from 'firebase/firestore';
import { firebaseAuth, firebaseFirestore } from '../config/firebase.web';

export interface AuthUser {
  uid: string;
  phoneNumber: string | null;
  displayName?: string | null;
  email?: string | null;
}

export interface UserDocument {
  uid: string;
  phoneNumber: string;
  displayName?: string;
  email?: string;
  role: 'customer' | 'driver';
  createdAt: any;
  updatedAt: any;
  isActive: boolean;
}

class AuthService {
  private recaptchaVerifier: RecaptchaVerifier | null = null;
  private confirmationResult: ConfirmationResult | null = null;

  // Initialize reCAPTCHA for phone auth (web requirement)
  private initializeRecaptcha(): void {
    if (!this.recaptchaVerifier) {
      this.recaptchaVerifier = new RecaptchaVerifier(firebaseAuth, 'recaptcha-container', {
        size: 'invisible',
        callback: () => {
          console.log('reCAPTCHA solved');
        },
        'expired-callback': () => {
          console.log('reCAPTCHA expired');
        }
      });
    }
  }

  async signInWithPhoneNumber(phoneNumber: string): Promise<boolean> {
    try {
      console.log('🔐 Starting phone authentication for:', phoneNumber);
      
      // Initialize reCAPTCHA
      this.initializeRecaptcha();
      
      if (!this.recaptchaVerifier) {
        throw new Error('Failed to initialize reCAPTCHA');
      }

      // Send SMS
      this.confirmationResult = await signInWithPhoneNumber(
        firebaseAuth, 
        phoneNumber, 
        this.recaptchaVerifier
      );
      
      console.log('✅ SMS sent successfully');
      return true;
    } catch (error: any) {
      console.error('❌ Phone authentication failed:', error);
      
      // Reset reCAPTCHA on error
      if (this.recaptchaVerifier) {
        this.recaptchaVerifier.clear();
        this.recaptchaVerifier = null;
      }
      
      throw new Error(error.message || 'Failed to send verification code');
    }
  }

  async verifyOTP(otp: string): Promise<AuthUser> {
    try {
      console.log('🔐 Verifying OTP:', otp);
      
      if (!this.confirmationResult) {
        throw new Error('No pending verification. Please request a new code.');
      }

      // Verify the OTP
      const userCredential = await this.confirmationResult.confirm(otp);
      const user = userCredential.user;
      
      if (!user) {
        throw new Error('Authentication failed');
      }

      console.log('✅ OTP verified successfully for user:', user.uid);

      // Create/update user document in Firestore
      await this.createOrUpdateUserDocument(user);

      // Clean up
      this.confirmationResult = null;
      if (this.recaptchaVerifier) {
        this.recaptchaVerifier.clear();
        this.recaptchaVerifier = null;
      }

      return {
        uid: user.uid,
        phoneNumber: user.phoneNumber,
        displayName: user.displayName,
        email: user.email,
      };
    } catch (error: any) {
      console.error('❌ OTP verification failed:', error);
      throw new Error(error.message || 'Invalid verification code');
    }
  }

  private async createOrUpdateUserDocument(user: User): Promise<void> {
    try {
      const userDocRef = doc(firebaseFirestore, 'users', user.uid);
      const userDoc = await getDoc(userDocRef);
      
      const userData: Partial<UserDocument> = {
        uid: user.uid,
        phoneNumber: user.phoneNumber || '',
        displayName: user.displayName || undefined,
        email: user.email || undefined,
        updatedAt: serverTimestamp(),
        isActive: true,
      };

      if (!userDoc.exists()) {
        // Create new user document
        const newUserData: UserDocument = {
          ...userData as UserDocument,
          role: 'customer', // Default role
          createdAt: serverTimestamp(),
        };
        
        await setDoc(userDocRef, newUserData);
        console.log('✅ User document created in Firestore');
      } else {
        // Update existing user document
        await setDoc(userDocRef, userData, { merge: true });
        console.log('✅ User document updated in Firestore');
      }
    } catch (error) {
      console.error('❌ Failed to create/update user document:', error);
      // Don't throw here - authentication was successful
    }
  }

  async signOut(): Promise<void> {
    try {
      await firebaseSignOut(firebaseAuth);
      console.log('✅ User signed out successfully');
    } catch (error) {
      console.error('❌ Sign out failed:', error);
      throw error;
    }
  }

  getCurrentUser(): AuthUser | null {
    const user = firebaseAuth.currentUser;
    if (!user) return null;

    return {
      uid: user.uid,
      phoneNumber: user.phoneNumber,
      displayName: user.displayName,
      email: user.email,
    };
  }

  onAuthStateChanged(callback: (user: AuthUser | null) => void): () => void {
    return onAuthStateChanged(firebaseAuth, (user) => {
      if (user) {
        callback({
          uid: user.uid,
          phoneNumber: user.phoneNumber,
          displayName: user.displayName,
          email: user.email,
        });
      } else {
        callback(null);
      }
    });
  }
}

export const authService = new AuthService();
