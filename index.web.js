import React from 'react';
import { createRoot } from 'react-dom/client';
import { AppRegistry } from 'react-native';

// Simple web app component
const WebApp = () => {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f5f5f5'
    }}>
      <div style={{
        backgroundColor: '#FFC100',
        padding: '20px',
        borderRadius: '10px',
        textAlign: 'center',
        boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{ margin: '0 0 10px 0', color: '#000' }}>🚕 YellowTaxi</h1>
        <p style={{ margin: '0', color: '#333' }}>React Native App Running on Web!</p>
        <p style={{ margin: '10px 0 0 0', fontSize: '14px', color: '#666' }}>
          This is a web version of the YellowTaxi mobile app
        </p>
      </div>
    </div>
  );
};

// Get the root element
const container = document.getElementById('root');
const root = createRoot(container);

// Render the app
root.render(<WebApp />);
